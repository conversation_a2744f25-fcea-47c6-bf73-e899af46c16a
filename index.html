<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webinar Go Live - Transform Videos into Live Webinar Sales Machines</title>
    <meta name="description" content="Turn any video into an engaging live webinar experience. Schedule, simulate live interactions, and convert more prospects with Webinar Go Live.">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); }
        .gradient-text { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
        .hero-pattern { background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0); background-size: 30px 30px; }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-8px); box-shadow: 0 25px 50px rgba(37, 99, 235, 0.15); }
        .animate-float { animation: float 6s ease-in-out infinite; }
        @keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-20px); } }
        .animate-pulse-slow { animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        .feature-icon { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); }
        .text-shadow { text-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .bg-gradient-light { background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); }
        .border-gradient { border: 2px solid transparent; background: linear-gradient(white, white) padding-box, linear-gradient(135deg, #2563eb, #1d4ed8) border-box; }
    </style>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2563eb',
                        secondary: '#1d4ed8',
                        accent: '#3b82f6',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <div class="w-10 h-10 gradient-bg rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-video text-white text-lg"></i>
                        </div>
                        <span class="text-2xl font-bold gradient-text">Webinar Go Live</span>
                    </div>
                </div>

                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">Features</a>
                    <a href="#pricing" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">Pricing</a>
                    <a href="#testimonials" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">Reviews</a>
                    <a href="#faq" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">FAQ</a>
                    <button class="bg-gray-100 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors font-medium">
                        Sign In
                    </button>
                    <button class="gradient-bg text-white px-6 py-2 rounded-lg hover:shadow-lg transition-all duration-300 font-semibold">
                        Start Free Trial
                    </button>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-btn" class="text-gray-600 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#features" class="block px-3 py-2 text-gray-600 hover:text-primary">Features</a>
                <a href="#pricing" class="block px-3 py-2 text-gray-600 hover:text-primary">Pricing</a>
                <a href="#testimonials" class="block px-3 py-2 text-gray-600 hover:text-primary">Reviews</a>
                <a href="#faq" class="block px-3 py-2 text-gray-600 hover:text-primary">FAQ</a>
                <button class="block w-full text-left px-3 py-2 text-gray-600 hover:text-primary">Sign In</button>
                <button class="block w-full gradient-bg text-white px-3 py-2 rounded-lg mt-2 font-semibold">Start Free Trial</button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 pt-20 pb-20 lg:pb-32 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div class="text-white">
                    <div class="inline-flex items-center bg-green-500/20 border border-green-400/30 rounded-full px-4 py-2 mb-6 backdrop-blur-sm">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                        <span class="text-sm font-medium text-green-100">Live Demo Available</span>
                    </div>

                    <h1 class="text-4xl lg:text-6xl font-bold leading-tight mb-6">
                        Transform Videos into
                        <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">Live Webinar</span>
                        Sales Machines
                    </h1>

                    <p class="text-xl lg:text-2xl text-gray-200 mb-8 leading-relaxed">
                        Turn any video into an engaging live webinar experience. Schedule, simulate live interactions, and convert 3x more prospects with authentic webinar automation.
                    </p>

                    <div class="flex flex-col sm:flex-row gap-4 mb-8">
                        <button class="bg-gradient-to-r from-orange-500 to-red-500 text-white px-8 py-4 rounded-xl font-bold text-lg hover:from-orange-600 hover:to-red-600 hover:shadow-2xl transition-all duration-300 flex items-center justify-center shadow-xl transform hover:scale-105">
                            <i class="fas fa-rocket mr-2"></i>
                            Start Free Trial
                        </button>
                        <button class="border-2 border-white bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-white hover:text-slate-900 transition-all duration-300 flex items-center justify-center">
                            <i class="fas fa-play mr-2"></i>
                            Watch Demo
                        </button>
                    </div>

                    <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-6 text-gray-200">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-400 mr-2 text-lg"></i>
                            <span class="font-medium">No Credit Card Required</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-400 mr-2 text-lg"></i>
                            <span class="font-medium">14-Day Free Trial</span>
                        </div>
                    </div>
                </div>

                <div class="relative">
                    <div class="relative z-10 animate-float">
                        <div class="bg-white rounded-2xl shadow-2xl p-6 border border-gray-200">
                            <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg p-4 mb-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-red-500 rounded-full mr-2 animate-pulse"></div>
                                        <span class="text-sm font-bold text-red-400">LIVE</span>
                                    </div>
                                    <div class="flex items-center text-sm text-gray-300">
                                        <i class="fas fa-eye mr-1"></i>
                                        <span class="font-semibold">247 watching</span>
                                    </div>
                                </div>
                                <div class="bg-gradient-to-br from-blue-600 to-purple-700 rounded-lg h-32 flex items-center justify-center relative overflow-hidden">
                                    <div class="absolute inset-0 bg-black/20"></div>
                                    <i class="fas fa-play text-white text-3xl relative z-10 drop-shadow-lg"></i>
                                </div>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex-shrink-0 flex items-center justify-center">
                                        <span class="text-white text-xs font-bold">J</span>
                                    </div>
                                    <div class="bg-gray-50 rounded-lg px-4 py-2 flex-1 border border-gray-200">
                                        <p class="text-sm font-medium text-gray-800">This is exactly what I needed! 🔥</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex-shrink-0 flex items-center justify-center">
                                        <span class="text-white text-xs font-bold">S</span>
                                    </div>
                                    <div class="bg-gray-50 rounded-lg px-4 py-2 flex-1 border border-gray-200">
                                        <p class="text-sm font-medium text-gray-800">Amazing content, taking notes!</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Floating elements -->
                    <div class="absolute -top-6 -right-6 w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full opacity-20 animate-pulse-slow"></div>
                    <div class="absolute -bottom-6 -left-6 w-20 h-20 bg-gradient-to-br from-pink-400 to-purple-500 rounded-full opacity-20 animate-pulse-slow"></div>
                    <div class="absolute top-1/2 -right-2 w-4 h-4 bg-blue-400 rounded-full opacity-60 animate-bounce"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="bg-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Everything You Need to Create
                    <span class="gradient-text">Converting Webinars</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Transform any video into an engaging live webinar experience with our comprehensive suite of tools
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="card-hover bg-white rounded-xl p-8 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 feature-icon rounded-lg flex items-center justify-center mb-6 shadow-lg">
                        <i class="fas fa-video text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Multi-Source Video Upload</h3>
                    <p class="text-gray-700 mb-4">Upload videos directly or use Vimeo/Wistia links. All video controls are completely hidden to create an authentic live experience.</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Direct MP4 file uploads</li>
                        <li>• Vimeo & Wistia integration</li>
                        <li>• Hidden video controls</li>
                    </ul>
                </div>

                <!-- Feature 2 -->
                <div class="card-hover bg-white rounded-xl p-8 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 feature-icon rounded-lg flex items-center justify-center mb-6 shadow-lg">
                        <i class="fas fa-calendar-check text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Multi-Date Scheduling</h3>
                    <p class="text-gray-700 mb-4">Schedule webinars across multiple dates and times with timezone support. Attendees choose their preferred slots.</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Multi-timezone support</li>
                        <li>• Multiple time slots</li>
                        <li>• Attendee slot selection</li>
                    </ul>
                </div>

                <!-- Feature 3 -->
                <div class="card-hover bg-white rounded-xl p-8 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 feature-icon rounded-lg flex items-center justify-center mb-6 shadow-lg">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Timed Comments & Links</h3>
                    <p class="text-gray-700 mb-4">Schedule specific comments with links at exact timestamps. Bulk import comments in "Name: Comment" format.</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Timestamp-based comments</li>
                        <li>• Bulk comment import</li>
                        <li>• Links in comments</li>
                    </ul>
                </div>

                <!-- Feature 4 -->
                <div class="card-hover bg-white rounded-xl p-8 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 feature-icon rounded-lg flex items-center justify-center mb-6 shadow-lg">
                        <i class="fas fa-magic text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Multi-Step Webinar Wizard</h3>
                    <p class="text-gray-700 mb-4">Create webinars through an intuitive step-by-step wizard. Configure all settings in one seamless flow.</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Step-by-step creation</li>
                        <li>• Intuitive interface</li>
                        <li>• All-in-one configuration</li>
                    </ul>
                </div>

                <!-- Feature 5 -->
                <div class="card-hover bg-white rounded-xl p-8 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 feature-icon rounded-lg flex items-center justify-center mb-6 shadow-lg">
                        <i class="fas fa-eye text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Dynamic Viewer Control</h3>
                    <p class="text-gray-700 mb-4">Define viewer count ranges and update intervals. Toggle consistent random comments during live simulation.</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Customizable viewer ranges</li>
                        <li>• Real-time updates</li>
                        <li>• Random comment toggle</li>
                    </ul>
                </div>

                <!-- Feature 6 -->
                <div class="card-hover bg-white rounded-xl p-8 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 feature-icon rounded-lg flex items-center justify-center mb-6 shadow-lg">
                        <i class="fas fa-share-alt text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Universal Embedding</h3>
                    <p class="text-gray-700 mb-4">Embed webinars anywhere - WordPress, websites, or generate shareable links for WhatsApp distribution.</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• WordPress compatibility</li>
                        <li>• Universal embed codes</li>
                        <li>• WhatsApp sharing</li>
                    </ul>
                </div>

                <!-- Feature 7 -->
                <div class="card-hover bg-white rounded-xl p-8 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 feature-icon rounded-lg flex items-center justify-center mb-6 shadow-lg">
                        <i class="fas fa-play-circle text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Real-Time Sync Experience</h3>
                    <p class="text-gray-700 mb-4">Late joiners see webinar from current timestamp (like Zoom). True live feel with synchronized playback.</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Zoom-like late joining</li>
                        <li>• Synchronized playback</li>
                        <li>• Authentic live feel</li>
                    </ul>
                </div>

                <!-- Feature 8 -->
                <div class="card-hover bg-white rounded-xl p-8 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 feature-icon rounded-lg flex items-center justify-center mb-6 shadow-lg">
                        <i class="fas fa-clipboard-list text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Custom Registration Forms</h3>
                    <p class="text-gray-700 mb-4">Build beautiful registration forms with custom fields. Collect attendee data before webinar access.</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Custom form fields</li>
                        <li>• Beautiful designs</li>
                        <li>• Pre-webinar data collection</li>
                    </ul>
                </div>

                <!-- Feature 9 -->
                <div class="card-hover bg-white rounded-xl p-8 border border-gray-200 shadow-sm">
                    <div class="w-12 h-12 feature-icon rounded-lg flex items-center justify-center mb-6 shadow-lg">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Advanced Analytics</h3>
                    <p class="text-gray-700 mb-4">Track join times, drop-off rates, watch percentages with progress bars and color-coded segments.</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Detailed retention analytics</li>
                        <li>• Progress bar visualization</li>
                        <li>• Color-coded segments</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section class="bg-gray-50 py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    How It Works
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Get your first converting webinar live in under 10 minutes
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <!-- Step 1 -->
                <div class="text-center">
                    <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white text-2xl font-bold">1</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Create with Multi-Step Wizard</h3>
                    <p class="text-gray-600">
                        Upload videos or add Vimeo/Wistia links through our intuitive wizard. Set exact duration for external videos to enable precise analytics tracking.
                    </p>
                </div>

                <!-- Step 2 -->
                <div class="text-center">
                    <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white text-2xl font-bold">2</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Schedule & Configure Experience</h3>
                    <p class="text-gray-600">
                        Set multiple dates/times across time zones, add timed comments with links, configure viewer counts, and create custom registration forms.
                    </p>
                </div>

                <!-- Step 3 -->
                <div class="text-center">
                    <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white text-2xl font-bold">3</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Deploy & Track Performance</h3>
                    <p class="text-gray-600">
                        Embed anywhere or share direct links. Monitor real-time analytics with join/drop-off times, watch percentages, and session continuity.
                    </p>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="gradient-bg text-white px-8 py-4 rounded-lg font-bold text-lg hover:opacity-90 transition-opacity">
                    Start Your First Webinar Now
                </button>
            </div>
        </div>
    </section>

    <!-- Final CTA -->
    <section class="gradient-bg py-20">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6">
                Ready to 3x Your Webinar Conversions?
            </h2>
            <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                Join thousands of businesses using Webinar Go Live to create engaging, converting webinar experiences that run on autopilot.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <button class="bg-white text-blue-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors shadow-xl">
                    <i class="fas fa-rocket mr-2"></i>
                    Start Free Trial
                </button>
                <button class="border-2 border-white bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-white hover:text-blue-600 transition-all duration-300">
                    <i class="fas fa-calendar mr-2"></i>
                    Schedule Demo
                </button>
            </div>

            <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-6 text-white/80">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>14-Day Free Trial</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>No Credit Card Required</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>Cancel Anytime</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 gradient-bg rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-video text-white text-lg"></i>
                        </div>
                        <span class="text-2xl font-bold">Webinar Go Live</span>
                    </div>
                    <p class="text-gray-400 mb-4 max-w-md">
                        Transform your videos into engaging live webinar experiences that convert 3x better than traditional presentations.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Product</h3>
                    <ul class="space-y-2">
                        <li><a href="#features" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="#pricing" class="text-gray-400 hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Integrations</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">API</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Support</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; 2024 Webinar Go Live. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Animate viewer count in hero
        function animateViewerCount() {
            const viewerElement = document.querySelector('.hero .text-gray-300 span');
            if (viewerElement) {
                let count = 247;
                setInterval(() => {
                    const change = Math.floor(Math.random() * 10) - 5;
                    count = Math.max(200, Math.min(300, count + change));
                    viewerElement.textContent = `${count} watching`;
                }, 3000);
            }
        }

        // Animate chat messages
        function animateChatMessages() {
            const messages = [
                { name: 'J', message: 'This is exactly what I needed! 🔥' },
                { name: 'S', message: 'Amazing content, taking notes!' },
                { name: 'M', message: 'When can I get started?' },
                { name: 'L', message: 'Perfect timing for my business!' },
                { name: 'R', message: 'Love the engagement features!' }
            ];

            const chatContainer = document.querySelector('.hero .space-y-3');
            if (chatContainer) {
                let messageIndex = 2;
                setInterval(() => {
                    const message = messages[messageIndex % messages.length];
                    const existingMessages = chatContainer.querySelectorAll('.flex.items-start');

                    if (existingMessages.length >= 2) {
                        existingMessages[0].remove();
                    }

                    const newMessage = document.createElement('div');
                    newMessage.className = 'flex items-start space-x-3';
                    newMessage.innerHTML = `
                        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex-shrink-0 flex items-center justify-center">
                            <span class="text-white text-xs font-bold">${message.name}</span>
                        </div>
                        <div class="bg-gray-50 rounded-lg px-4 py-2 flex-1 border border-gray-200">
                            <p class="text-sm font-medium text-gray-800">${message.message}</p>
                        </div>
                    `;

                    chatContainer.appendChild(newMessage);
                    messageIndex++;
                }, 4000);
            }
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            animateViewerCount();
            animateChatMessages();
        });
    </script>
</body>
</html>
