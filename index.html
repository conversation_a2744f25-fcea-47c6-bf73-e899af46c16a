<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webinar Go Live - Turn Videos into Live Webinar Sales Machines</title>
    <meta name="description" content="Transform your sales videos and meetings into engaging live webinar experiences. Perfect for coaches and consultants to boost conversions.">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                        display: ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                    },
                    backgroundImage: {
                        'blue-gradient': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        'blue-gradient-2': 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',
                        'hero-gradient': 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #3b82f6 100%)',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                    },
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <style>
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            transform: scale(1.05);
        }
        .btn-secondary {
            background: white;
            color: #2563eb;
            border: 2px solid #2563eb;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background: #eff6ff;
        }
        .gradient-text {
            background: linear-gradient(to right, #2563eb, #7c3aed);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
    </style>
</head>
<body class="font-sans antialiased">
    <!-- Navigation -->
    <nav class="fixed w-full z-50 bg-white/90 backdrop-blur-md border-b border-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5v10l7-5-7-5z"/>
                        </svg>
                    </div>
                    <span class="text-xl font-bold font-display gradient-text">
                        Webinar Go Live
                    </span>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="text-gray-600 hover:text-primary-600 transition-colors">Features</a>
                    <a href="#how-it-works" class="text-gray-600 hover:text-primary-600 transition-colors">How It Works</a>
                    <a href="#pricing" class="text-gray-600 hover:text-primary-600 transition-colors">Pricing</a>
                    <a href="#testimonials" class="text-gray-600 hover:text-primary-600 transition-colors">Reviews</a>
                    <a href="#" class="text-gray-600 hover:text-primary-600 transition-colors">Login</a>
                    <a href="#" class="btn-primary text-white font-semibold py-3 px-6 rounded-lg shadow-lg">Get Started</a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-600 hover:text-primary-600 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#features" class="block px-3 py-2 text-gray-600 hover:text-primary-600">Features</a>
                <a href="#how-it-works" class="block px-3 py-2 text-gray-600 hover:text-primary-600">How It Works</a>
                <a href="#pricing" class="block px-3 py-2 text-gray-600 hover:text-primary-600">Pricing</a>
                <a href="#testimonials" class="block px-3 py-2 text-gray-600 hover:text-primary-600">Reviews</a>
                <a href="#" class="block px-3 py-2 text-gray-600 hover:text-primary-600">Login</a>
                <a href="#" class="block w-full btn-primary text-white px-3 py-2 rounded-lg mt-2 font-semibold">Get Started</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-20 pb-16 bg-gradient-to-br from-blue-50 via-white to-purple-50 overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Left Column - Content -->
                <div class="space-y-8 animate-fade-in">
                    <div class="space-y-4">
                        <div class="inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Transform Your Sales Process</span>
                        </div>

                        <h1 class="text-4xl md:text-6xl font-bold font-display leading-tight">
                            Turn Any Video Into a
                            <span class="gradient-text">Live Webinar</span>
                            Sales Machine
                        </h1>

                        <p class="text-xl text-gray-600 leading-relaxed">
                            Perfect for coaches and consultants. Create engaging webinar experiences
                            that feel completely live, with scheduled comments, viewer tracking,
                            and powerful analytics to boost your conversions.
                        </p>
                    </div>

                    <!-- Stats -->
                    <div class="flex flex-wrap gap-8">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-600">10,000+ Active Users</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">5-Min Setup</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">3x Higher Conversions</span>
                        </div>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="#" class="btn-primary text-white font-semibold py-3 px-6 rounded-lg shadow-lg inline-flex items-center justify-center space-x-2">
                            <span>Start Free Trial</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                        <a href="#demo" class="btn-secondary font-semibold py-3 px-6 rounded-lg inline-flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 5v10l7-5-7-5z"/>
                            </svg>
                            <span>Watch Demo</span>
                        </a>
                    </div>
                </div>

                <!-- Right Column - Visual -->
                <div class="relative animate-slide-up">
                    <div class="relative">
                        <!-- Main Dashboard Mockup -->
                        <div class="bg-white rounded-2xl shadow-2xl p-6 border border-gray-100">
                            <div class="flex items-center space-x-3 mb-4">
                                <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                            </div>

                            <!-- Fake Video Player -->
                            <div class="bg-gray-900 rounded-lg aspect-video relative overflow-hidden mb-4">
                                <div class="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600 opacity-80"></div>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                                        <svg class="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M8 5v10l7-5-7-5z"/>
                                        </svg>
                                    </div>
                                </div>

                                <!-- Live Badge -->
                                <div class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                                    <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                                    <span>LIVE</span>
                                </div>

                                <!-- Viewer Count -->
                                <div class="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                                    247 viewers
                                </div>
                            </div>

                            <!-- Comments Section -->
                            <div class="space-y-2">
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-start space-x-2">
                                        <div class="w-6 h-6 bg-blue-500 rounded-full flex-shrink-0"></div>
                                        <div>
                                            <p class="text-sm font-medium">Sarah M.</p>
                                            <p class="text-sm text-gray-600">This is exactly what I needed for my coaching business!</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-start space-x-2">
                                        <div class="w-6 h-6 bg-green-500 rounded-full flex-shrink-0"></div>
                                        <div>
                                            <p class="text-sm font-medium">Mike R.</p>
                                            <p class="text-sm text-gray-600">Amazing results! My conversion rate doubled.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Floating Elements -->
                        <div class="absolute -top-4 -right-4 bg-white rounded-lg shadow-lg p-3 border border-gray-100">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span class="text-sm font-medium">Live Analytics</span>
                            </div>
                        </div>

                        <div class="absolute -bottom-4 -left-4 bg-white rounded-lg shadow-lg p-3 border border-gray-100">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-primary-600">85%</div>
                                <div class="text-xs text-gray-500">Retention Rate</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold font-display mb-4">
                    Everything You Need to Create
                    <span class="gradient-text">Engaging Webinars</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Transform your sales videos into powerful webinar experiences with features
                    designed specifically for coaches and consultants.
                </p>
            </div>

            <!-- Features Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="group p-6 bg-white rounded-xl border border-gray-100 hover:border-primary-200 card-hover">
                    <div class="flex items-start space-x-4">
                        <div class="bg-blue-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                                Multi-Source Video Upload
                            </h3>
                            <p class="text-gray-600 leading-relaxed">
                                Upload MP4 files or use Vimeo/Wistia links. All video controls are hidden for authentic live experience.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Feature 2 -->
                <div class="group p-6 bg-white rounded-xl border border-gray-100 hover:border-primary-200 card-hover">
                    <div class="flex items-start space-x-4">
                        <div class="bg-green-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                                Time-Synchronized Playback
                            </h3>
                            <p class="text-gray-600 leading-relaxed">
                                Viewers who join late automatically start from the current timestamp, just like real live events.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Feature 3 -->
                <div class="group p-6 bg-white rounded-xl border border-gray-100 hover:border-primary-200 card-hover">
                    <div class="flex items-start space-x-4">
                        <div class="bg-purple-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                                Scheduled Comments & Links
                            </h3>
                            <p class="text-gray-600 leading-relaxed">
                                Pre-schedule comments with timestamps and links to appear at specific moments during your webinar.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Feature 4 -->
                <div class="group p-6 bg-white rounded-xl border border-gray-100 hover:border-primary-200 card-hover">
                    <div class="flex items-start space-x-4">
                        <div class="bg-orange-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                                Dynamic Viewer Count
                            </h3>
                            <p class="text-gray-600 leading-relaxed">
                                Realistic viewer count simulation that updates at intervals to create authentic live engagement.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Feature 5 -->
                <div class="group p-6 bg-white rounded-xl border border-gray-100 hover:border-primary-200 card-hover">
                    <div class="flex items-start space-x-4">
                        <div class="bg-cyan-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                                Advanced Analytics
                            </h3>
                            <p class="text-gray-600 leading-relaxed">
                                Track individual viewer retention, engagement metrics, and conversion rates with detailed insights.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Feature 6 -->
                <div class="group p-6 bg-white rounded-xl border border-gray-100 hover:border-primary-200 card-hover">
                    <div class="flex items-start space-x-4">
                        <div class="bg-pink-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                                Multi-Timezone Support
                            </h3>
                            <p class="text-gray-600 leading-relaxed">
                                Schedule webinars across different time zones with automatic conversion and local time display.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Feature 7 -->
                <div class="group p-6 bg-white rounded-xl border border-gray-100 hover:border-primary-200 card-hover">
                    <div class="flex items-start space-x-4">
                        <div class="bg-indigo-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                                Custom Registration Forms
                            </h3>
                            <p class="text-gray-600 leading-relaxed">
                                Build beautiful registration forms with custom fields to collect attendee data before webinar access.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Feature 8 -->
                <div class="group p-6 bg-white rounded-xl border border-gray-100 hover:border-primary-200 card-hover">
                    <div class="flex items-start space-x-4">
                        <div class="bg-red-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                                Smart Email Notifications
                            </h3>
                            <p class="text-gray-600 leading-relaxed">
                                SendGrid integration for automated reminders with customizable timing and email templates.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Feature 9 -->
                <div class="group p-6 bg-white rounded-xl border border-gray-100 hover:border-primary-200 card-hover">
                    <div class="flex items-start space-x-4">
                        <div class="bg-teal-500 p-3 rounded-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold mb-2 group-hover:text-primary-600 transition-colors">
                                Instant Embed Anywhere
                            </h3>
                            <p class="text-gray-600 leading-relaxed">
                                Generate embed codes to seamlessly integrate your webinars into WordPress, landing pages, or any website.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section id="how-it-works" class="bg-gray-50 py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold font-display mb-4">
                    How It Works
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Get your first converting webinar live in under 10 minutes
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <!-- Step 1 -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white text-2xl font-bold">1</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Upload Your Video</h3>
                    <p class="text-gray-600">
                        Upload an MP4 file or paste a Vimeo/Wistia URL. Our platform handles the rest automatically with our multi-step wizard.
                    </p>
                </div>

                <!-- Step 2 -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white text-2xl font-bold">2</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Customize Experience</h3>
                    <p class="text-gray-600">
                        Set up chat comments, viewer counts, scheduling, and registration forms to match your brand and audience.
                    </p>
                </div>

                <!-- Step 3 -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white text-2xl font-bold">3</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Launch & Convert</h3>
                    <p class="text-gray-600">
                        Share your webinar link or embed it on your site. Watch conversions increase automatically with detailed analytics.
                    </p>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="#" class="btn-primary text-white px-8 py-4 rounded-lg font-bold text-lg hover:opacity-90 transition-opacity">
                    Start Your First Webinar Now
                </a>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold font-display mb-4">
                    Simple, Transparent Pricing
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Choose the perfect plan for your business. All plans include a 14-day free trial.
                </p>
            </div>

            <!-- Pricing Cards -->
            <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                <!-- Starter Plan -->
                <div class="bg-white rounded-2xl shadow-lg border-2 border-gray-100 p-8 card-hover">
                    <div class="text-center mb-8">
                        <div class="w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>

                        <h3 class="text-2xl font-bold mb-2">Starter</h3>
                        <p class="text-gray-600 mb-4">Perfect for getting started with webinar marketing</p>

                        <div class="flex items-baseline justify-center">
                            <span class="text-4xl font-bold">$29</span>
                            <span class="text-gray-500 ml-2">/month</span>
                        </div>
                    </div>

                    <ul class="space-y-3 mb-8">
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">5 webinars per month</span>
                        </li>
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">Up to 100 viewers per webinar</span>
                        </li>
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">Basic analytics</span>
                        </li>
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">Email support</span>
                        </li>
                    </ul>

                    <button class="w-full py-3 px-6 bg-gray-100 text-gray-900 hover:bg-gray-200 rounded-lg font-semibold transition-all duration-300">
                        Start Free Trial
                    </button>
                </div>

                <!-- Professional Plan (Popular) -->
                <div class="relative bg-white rounded-2xl shadow-lg border-2 border-primary-500 p-8 transform scale-105 card-hover">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <div class="bg-primary-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                            Most Popular
                        </div>
                    </div>

                    <div class="text-center mb-8">
                        <div class="w-12 h-12 mx-auto mb-4 bg-primary-600 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3l14 9-14 9V3z"></path>
                            </svg>
                        </div>

                        <h3 class="text-2xl font-bold mb-2">Professional</h3>
                        <p class="text-gray-600 mb-4">Ideal for coaches and consultants scaling their business</p>

                        <div class="flex items-baseline justify-center">
                            <span class="text-4xl font-bold">$79</span>
                            <span class="text-gray-500 ml-2">/month</span>
                        </div>
                    </div>

                    <ul class="space-y-3 mb-8">
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">25 webinars per month</span>
                        </li>
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">Up to 500 viewers per webinar</span>
                        </li>
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">Advanced analytics & retention tracking</span>
                        </li>
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">Scheduled comments with links</span>
                        </li>
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">Priority support</span>
                        </li>
                    </ul>

                    <button class="w-full py-3 px-6 bg-primary-600 text-white hover:bg-primary-700 rounded-lg font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                        Start Free Trial
                    </button>
                </div>

                <!-- Enterprise Plan -->
                <div class="bg-white rounded-2xl shadow-lg border-2 border-gray-100 p-8 card-hover">
                    <div class="text-center mb-8">
                        <div class="w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>

                        <h3 class="text-2xl font-bold mb-2">Enterprise</h3>
                        <p class="text-gray-600 mb-4">For agencies and large organizations</p>

                        <div class="flex items-baseline justify-center">
                            <span class="text-4xl font-bold">$199</span>
                            <span class="text-gray-500 ml-2">/month</span>
                        </div>
                    </div>

                    <ul class="space-y-3 mb-8">
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">Unlimited webinars</span>
                        </li>
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">Unlimited viewers</span>
                        </li>
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">White-label solution</span>
                        </li>
                        <li class="flex items-start space-x-3">
                            <svg class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-600">24/7 phone support</span>
                        </li>
                    </ul>

                    <button class="w-full py-3 px-6 bg-gray-100 text-gray-900 hover:bg-gray-200 rounded-lg font-semibold transition-all duration-300">
                        Contact Sales
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 gradient-bg rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-video text-white text-lg"></i>
                        </div>
                        <span class="text-2xl font-bold">Webinar Go Live</span>
                    </div>
                    <p class="text-gray-400 mb-4 max-w-md">
                        Transform your videos into engaging live webinar experiences that convert 3x better than traditional presentations.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Product</h3>
                    <ul class="space-y-2">
                        <li><a href="#features" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="#pricing" class="text-gray-400 hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Integrations</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">API</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Support</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; 2024 Webinar Go Live. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Animate viewer count in hero
        function animateViewerCount() {
            const viewerElement = document.querySelector('.hero .text-gray-300 span');
            if (viewerElement) {
                let count = 247;
                setInterval(() => {
                    const change = Math.floor(Math.random() * 10) - 5;
                    count = Math.max(200, Math.min(300, count + change));
                    viewerElement.textContent = `${count} watching`;
                }, 3000);
            }
        }

        // Animate chat messages
        function animateChatMessages() {
            const messages = [
                { name: 'J', message: 'This is exactly what I needed! 🔥' },
                { name: 'S', message: 'Amazing content, taking notes!' },
                { name: 'M', message: 'When can I get started?' },
                { name: 'L', message: 'Perfect timing for my business!' },
                { name: 'R', message: 'Love the engagement features!' }
            ];

            const chatContainer = document.querySelector('.hero .space-y-3');
            if (chatContainer) {
                let messageIndex = 2;
                setInterval(() => {
                    const message = messages[messageIndex % messages.length];
                    const existingMessages = chatContainer.querySelectorAll('.flex.items-start');

                    if (existingMessages.length >= 2) {
                        existingMessages[0].remove();
                    }

                    const newMessage = document.createElement('div');
                    newMessage.className = 'flex items-start space-x-3';
                    newMessage.innerHTML = `
                        <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex-shrink-0 flex items-center justify-center">
                            <span class="text-white text-xs font-bold">${message.name}</span>
                        </div>
                        <div class="bg-gray-50 rounded-lg px-4 py-2 flex-1 border border-gray-200">
                            <p class="text-sm font-medium text-gray-800">${message.message}</p>
                        </div>
                    `;

                    chatContainer.appendChild(newMessage);
                    messageIndex++;
                }, 4000);
            }
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            animateViewerCount();
            animateChatMessages();
        });
    </script>
</body>
</html>
