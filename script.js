// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Mobile Navigation Toggle
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // Smooth Scrolling for Navigation Links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });

    // Navbar Background on Scroll
    const navbar = document.querySelector('.navbar');
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    });

    // Animated Counter for Hero Stats
    function animateCounter(element, target, duration = 2000) {
        let start = 0;
        const increment = target / (duration / 16);
        
        function updateCounter() {
            start += increment;
            if (start < target) {
                element.textContent = Math.floor(start).toLocaleString();
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target.toLocaleString();
            }
        }
        
        updateCounter();
    }

    // Intersection Observer for Animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                
                // Animate counters when hero stats come into view
                if (entry.target.classList.contains('hero-stats')) {
                    const statNumbers = entry.target.querySelectorAll('.stat-number');
                    statNumbers.forEach((stat, index) => {
                        const text = stat.textContent;
                        let target = 0;
                        
                        if (text.includes('10,000+')) {
                            target = 10000;
                            setTimeout(() => {
                                animateCounter(stat, target);
                                stat.textContent = '10,000+';
                            }, index * 200);
                        } else if (text.includes('95%')) {
                            target = 95;
                            setTimeout(() => {
                                animateCounter(stat, target);
                                stat.textContent = '95%';
                            }, index * 200);
                        } else if (text.includes('3x')) {
                            stat.textContent = '3x';
                        }
                    });
                }
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .step, .hero-stats');
    animateElements.forEach(el => observer.observe(el));

    // Simulate Chat Messages in Hero Mockup
    const chatMessages = [
        { name: 'Sarah', message: 'This is amazing!' },
        { name: 'Mike', message: 'When can I get started?' },
        { name: 'Lisa', message: 'Perfect timing for my business!' },
        { name: 'John', message: 'Love the engagement features!' },
        { name: 'Emma', message: 'This will save me so much time!' }
    ];

    let messageIndex = 0;
    const chatContainer = document.querySelector('.chat-simulation');
    
    function addChatMessage() {
        if (chatContainer && messageIndex < chatMessages.length) {
            const message = chatMessages[messageIndex];
            const messageElement = document.createElement('div');
            messageElement.className = 'chat-message';
            messageElement.innerHTML = `<strong>${message.name}:</strong> ${message.message}`;
            
            // Remove oldest message if more than 3
            const existingMessages = chatContainer.querySelectorAll('.chat-message');
            if (existingMessages.length >= 3) {
                existingMessages[0].remove();
            }
            
            chatContainer.appendChild(messageElement);
            messageIndex = (messageIndex + 1) % chatMessages.length;
        }
    }

    // Add initial messages and continue cycling
    setTimeout(() => {
        addChatMessage();
        setInterval(addChatMessage, 3000);
    }, 2000);

    // Simulate Viewer Count Updates
    const viewerCountElement = document.querySelector('.viewer-count');
    if (viewerCountElement) {
        let viewerCount = 247;
        setInterval(() => {
            const change = Math.floor(Math.random() * 10) - 5; // Random change between -5 and +4
            viewerCount = Math.max(200, Math.min(300, viewerCount + change));
            viewerCountElement.innerHTML = `<i class="fas fa-eye"></i> ${viewerCount} viewers`;
        }, 5000);
    }

    // Button Click Handlers
    const ctaButtons = document.querySelectorAll('.btn-primary, .btn-secondary, .btn-outline');
    ctaButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // Handle different button actions
            const buttonText = this.textContent.toLowerCase();
            if (buttonText.includes('demo') || buttonText.includes('watch')) {
                // Demo button clicked
                showModal('Demo Video', 'Demo video would be embedded here.');
            } else if (buttonText.includes('trial') || buttonText.includes('started')) {
                // Sign up button clicked
                showModal('Get Started', 'Sign up form would be displayed here.');
            } else if (buttonText.includes('schedule')) {
                // Schedule demo button clicked
                showModal('Schedule Demo', 'Calendar booking widget would be embedded here.');
            }
        });
    });

    // Simple Modal Function
    function showModal(title, content) {
        // Create modal if it doesn't exist
        let modal = document.getElementById('simple-modal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'simple-modal';
            modal.innerHTML = `
                <div class="modal-overlay">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title"></h3>
                            <button class="modal-close">&times;</button>
                        </div>
                        <div class="modal-body"></div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Add modal styles
            const modalStyles = `
                #simple-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 10000;
                    display: none;
                }
                .modal-overlay {
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 20px;
                }
                .modal-content {
                    background: white;
                    border-radius: 15px;
                    max-width: 500px;
                    width: 100%;
                    max-height: 80vh;
                    overflow-y: auto;
                }
                .modal-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px 30px;
                    border-bottom: 1px solid #eee;
                }
                .modal-title {
                    margin: 0;
                    color: #333;
                }
                .modal-close {
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #999;
                }
                .modal-close:hover {
                    color: #333;
                }
                .modal-body {
                    padding: 30px;
                    color: #666;
                }
            `;
            
            const styleSheet = document.createElement('style');
            styleSheet.textContent = modalStyles;
            document.head.appendChild(styleSheet);

            // Close modal handlers
            modal.querySelector('.modal-close').addEventListener('click', () => {
                modal.style.display = 'none';
            });
            
            modal.querySelector('.modal-overlay').addEventListener('click', (e) => {
                if (e.target === modal.querySelector('.modal-overlay')) {
                    modal.style.display = 'none';
                }
            });
        }

        // Update modal content and show
        modal.querySelector('.modal-title').textContent = title;
        modal.querySelector('.modal-body').textContent = content;
        modal.style.display = 'block';
    }

    // Add CSS animations
    const animationStyles = `
        .animate-in {
            animation: fadeInUp 0.8s ease forwards;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .nav-menu.active {
            display: flex !important;
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background: white;
            flex-direction: column;
            padding: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .hamburger.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }
        
        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }
        
        .hamburger.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.textContent = animationStyles;
    document.head.appendChild(styleSheet);
});

// Parallax Effect for Hero Section
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const heroImage = document.querySelector('.hero-image');
    if (heroImage) {
        heroImage.style.transform = `translateY(${scrolled * 0.1}px)`;
    }
});
